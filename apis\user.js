import { $get, $post } from '@/utils/request'

/**
 * 人机验证行为校验接口
 * @param {Object} data - 请求参数
 * @param {string} data.phone - 用户手机号
 * @param {string} data.template - 模板信息（可选）
 * @param {string} data.channelId - 渠道ID
 * @param {string} data.captchaVerifyParam - 阿里云人机验证返回的验证参数
 * @returns {Promise} 返回验证结果
 */
export const behaviorVerify = (data = {}) => {
  return $post({
    url: '/user/behaviorVerify',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

/**
 * 保存用户资质信息接口
 * @param {Object} data - 请求参数
 * @param {string} data.smsId - 短信ID（可选）
 * @param {string} data.channelId - 渠道ID
 * @param {string} data.consumerId - 用户ID
 * @param {number} data.sesameId - 芝麻分ID
 * @param {boolean} data.isOverdue - 是否逾期
 * @param {string} data.cityCode - 城市代码（可选）
 * @param {string} data.name - 姓名（可选）
 * @param {number} data.age - 年龄（可选）
 * @param {number} data.sex - 性别（可选）
 * @param {string} data.phone - 手机号（可选）
 * @param {boolean} data.isProvident - 是否有公积金（可选）
 * @param {boolean} data.isHouse - 是否有房产（可选）
 * @param {boolean} data.isVehicle - 是否有车辆（可选）
 * @param {boolean} data.isSocial - 是否有社保（可选）
 * @param {boolean} data.isInsure - 是否有保险单（可选）
 * @param {boolean} data.isBusiness - 是否有营业执照（可选）
 * @returns {Promise} 返回保存结果
 */
export const saveAptitude = (data = {}) => {
  return $post({
    url: '/user/save/aptitude',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}