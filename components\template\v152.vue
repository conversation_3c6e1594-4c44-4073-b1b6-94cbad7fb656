<template>
  <view class="page-container">
    <view class="tips-container">
      <view class="tips-content">
        <view class="tips-img-container">
          <image
            src="https://cdn.oss-unos.hmctec.cn/common/path/472c3918d23b4d2e9d763317f23758ea.png"
            mode="scaleToFill"
            class="tips-img"
          />
        </view>

        <uni-notice-bar
          background-color="transparent"
          color="#ffffff"
          single
          scrollable
          class="tips-text"
          text="温馨提示:所有贷款在未放成功放款前，绝不收取任何费用"
        />
      </view>
    </view>

    <view class="amount-container">
      <view class="amount-title">最高可借200000(元)</view>
      <view class="amount-input-container">
        <input class="amount-input" type="number" v-model="amount" @blur="amountInputBlur" />
        <image
          class="amount-input-img"
          src="https://cdn.oss-unos.hmctec.cn/common/path/d1997fbcbbeb4d13a38e462b38853341.png"
          mode="scaleToFill"
          @click="clearAmountClick"
        />
      </view>
      <view class="amount-rate-container">
        参考年化利率
        <text class="amount-rate-text">7.2%</text>
        起,1000元1天仅需0.3元
      </view>
    </view>

    <view class="amount-info-container">
      <view class="amount-info-title">还款期数</view>
      <view class="amount-info-list">
        <view class="amount-info-list-item" v-for="item in periodList" :key="item" :class="{ selected: item === period }" @click="periodClick(item)">{{ item }}期</view>
      </view>

      <view class="amount-info-plan">
        <view class="amount-info-plan-title">还款计划</view>
        <view class="amount-info-plan-amount">应还{{ monthlyPay }}元</view>
      </view>

      <view class="amount-info-tips">最终借款额度，借款利率以实际审批为准</view>
    </view>

    <view class="amount-info-input-container">
      <view class="amount-info-input-item">
        <input class="phone-input" type="number" placeholder="请输入手机号(已加密）" v-model="phone" @blur="phoneInputBlur" maxlength="11" />
      </view>

      <!-- 人机验证组件 -->
      <aliyun-captcha
        v-show="shouldShowCaptcha"
        @success="onCaptchaSuccess"
        @fail="onCaptchaFail"
        @error="onCaptchaError"
      >
        <view class="amount-info-btn"> 领取我的额度 </view>
      </aliyun-captcha>

      <!-- 普通按钮 -->
      <view v-show="!shouldShowCaptcha" class="amount-info-btn" @click="clickGetAmount"> 领取我的额度 </view>
    </view>

    <view class="declare-container">
      <view>郑重声明</view>
      <view
        >本平台只提供贷款咨询和推荐服务，
        放款由银行或金融机构进行，所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全</view
      >
      <view>请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。证您的资金安全</view>
    </view>
  </view>
</template>

<script>
import { reportUV, getDcProductLink, saveAptitude } from '@/apis/common'
import { behaviorVerify } from '@/apis/user'
import { encryptByDES } from '@/utils/encrypt'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import AliyunCaptcha from '@/components/captcha/aliyun-captcha.vue'

export default {
  name: 'v152',
  components: {
    AliyunCaptcha
  },

  computed: {
    shouldShowCaptcha() {
      return this.$u.test.mobile(this.phone);
    }
  },

  data() {
    return {
      amount: 200000,
      periodList: [36, 24, 12, 6],
      period: 36,
      monthlyPay: '',
      phone: '',
      captchaVerifyParam: null
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },


  mounted() {
    if (this.channelId) {
      // 上报UV
      reportUV({ channelId: this.channelId })
    }
    this.computedMonthPay()
    uni.setNavigationBarTitle({
      title: '聚数通'
    })
  },
  methods: {
    amountInputBlur() {
      if (isNaN(this.amount)) {
        this.amount = 200000
        return
      }

      if (this.amount < 50000) {
        this.amount = 50000
        return
      }

      if (this.amount > 200000) {
        this.amount = 200000
        return
      }

      this.computedMonthPay()
    },

    clearAmountClick() {
      this.amount = 200000
      this.computedMonthPay()
    },

    periodClick(item) {
      this.period = item
      this.computedMonthPay()
    },

    computedMonthPay() {
      let price = Number(this.amount)
      let mLatte = (price * 12) / 100 / 12
      const month = this.period

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    phoneInputBlur() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },



    clickGetAmount() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },

    // 人机验证成功回调
    async onCaptchaSuccess(captchaVerifyParam) {
      this.captchaVerifyParam = captchaVerifyParam

      // 显示验证成功提示
      uni.showToast({
        title: '验证成功',
        icon: 'success',
        duration: 1500
      })

      // 先调用行为验证接口进行后端校验
      try {
        uni.showLoading({
          title: '验证中...',
          mask: true
        })

        const verifyRes = await behaviorVerify({
          phone: this.phone,
          template: 'v152',
          channelId: this.channelId,
          captchaVerifyParam
        })

        uni.hideLoading()

        if (verifyRes.code !== 200 || verifyRes.data !== true) {
          uni.showToast({
            title: '验证失败',
            icon: 'none'
          })
          this.resetCaptcha()
          return
        }

        // 验证通过后，直接进行登录
        this.login()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        })
        this.resetCaptcha()
      }
    },

    // 人机验证失败回调
    onCaptchaFail(error) {
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 人机验证错误回调
    onCaptchaError(error) {
      uni.showToast({
        title: '验证组件加载失败',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 重置验证状态
    resetCaptcha() {
      this.captchaVerifyParam = null;
    },

    async getDcProductLink(consumerId) {
      uni.showLoading({
        title: '加载中',
        mask: true
      })

      try {
        const res = await getDcProductLink({ consumerId })
        return res.data || ''
      } catch (error) {
        return ''
      } finally {
        uni.hideLoading()
      }
    },

    async login() {
      try {
        const params = {
          phone: this.phone,
          channelId: this.channelId,
          demandAmount: this.amount,
          deviceType: uni.getSystemInfoSync().platform,
          phoneBlack: getBlackPhone()
        }

        const res = await saveAptitude(params)

        if (res.code != 200) {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          })
          return
        }

        // 登录成功后，记录手机号到黑名单
        setBlackPhone(params.phone)
        this.$u.vuex('vuex_phone', params.phone)
        this.$u.vuex('vuex_consumerId', res.data)

        const urlParam = {
          consumerId: res.data,
          phone: this.phone,
          demandAmount: this.amount,
          channelId: this.channelId,
          monthIndex: this.periodList.indexOf(this.period)
        }

        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

        const link = await this.getDcProductLink(urlParam.consumerId)
        this.$u.vuex('vuex_overloanWebview.url', link)

        if (link) {
          uni.navigateTo({
            url: `/extreme/v152/overloanWebview/index?param=${urlParamString}`
          })
        } else {
          uni.navigateTo({
            url: `/extreme/v152/wechatOfficialAccount/index?param=${urlParamString}`
          })
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #304ac8 43%, rgba(216, 216, 216, 0) 100%);
}

.tips-container {
  .tips-content {
    display: flex;
    align-items: center;
    gap: 10rpx;
    background: rgba(255, 255, 255, 0.19);
    padding: 18rpx 35rpx;

    .tips-img-container {
      width: 40rpx;
      height: 40rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .tips-img {
      width: 26rpx;
      height: 27rpx;
    }

    .tips-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 40rpx;
      margin: 0;
      padding: 0;
    }
  }
}

.amount-container {
  padding: 40px 30rpx 60rpx;
  margin: 30rpx 30rpx 20rpx;
  background: #f4f6ff;
  border-radius: 20rpx;

  .amount-title {
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
    text-align: center;
  }

  .amount-input-container {
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25rpx;
    border-bottom: 1rpx solid #d8d8d8;
    padding-bottom: 20rpx;

    .amount-input {
      font-family: D-DIN, D-DIN;
      font-weight: 700;
      font-size: 98rpx;
      color: #304ac8;
      line-height: 137rpx;
      width: 300rpx;
    }

    .amount-input-img {
      width: 28rpx;
      height: 28rpx;
      flex-shrink: 0;
      transform: translateY(20rpx);
    }
  }

  .amount-rate-container {
    margin-top: 20rpx;

    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 36rpx;
    text-align: center;

    .amount-rate-text {
      color: #ff8827;
    }
  }
}

.amount-info-container {
  padding: 30rpx 30rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0 30rpx;

  .amount-info-title {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 41rpx;
  }

  .amount-info-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 34rpx;
    gap: 30rpx;

    .amount-info-list-item {
      width: 133rpx;
      height: 60rpx;
      background: #f7f7f7;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 1rpx solid #f7f7f7;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Arial, Arial;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      &.selected {
        background: #d6ddff;
        color: #304ac8;
        border-color: #304ac8;
      }
    }
  }

  .amount-info-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 57rpx;

    .amount-info-plan-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
    }

    .amount-info-plan-amount {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;
    }
  }

  .amount-info-tips {
    margin-top: 12rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 35rpx;
  }
}

.amount-info-input-container {
  margin: 20rpx 30rpx 40rpx;
  padding: 45rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;

  .amount-info-input-item {
    padding: 32rpx 40rpx;
    background: #ffffff;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    border: 2rpx solid #d1d1d1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15rpx;

    &.code-input-item {
      margin-bottom: 35rpx;
    }

    .phone-input,
    .code-input {
      font-weight: 400;
      font-size: 32rpx;
      line-height: 36rpx;
    }

    .code-input-btn {
      font-weight: 400;
      font-size: 32rpx;
      color: #304ac8;
      line-height: 36rpx;
    }
  }

  .amount-info-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #304ac8;
    border-radius: 546rpx 546rpx 546rpx 546rpx;
    padding: 22rpx;
    font-weight: 400;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 58rpx;
  }
}

.declare-container {
  padding: 0 60rpx;
  font-weight: normal;
  font-size: 24rpx;
  color: #939393;
  line-height: 34rpx;
  text-align: center;
}
</style>
