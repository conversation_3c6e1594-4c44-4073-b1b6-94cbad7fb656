<template>
  <view class="page-container">
    <view class="amount-container">
      <view class="amount-title">借款金额(元)</view>
      <view class="amount-input-container">
        <view class="amount-input-icon"> ￥ </view>
        <input class="amount-input" type="number" v-model="amount" @blur="amountInputBlur" />
        <image
          class="amount-input-img"
          src="https://cdn.oss-unos.hmctec.cn/common/path/33f0343ec29b489b91b9fb7bbd4fc14c.png"
          mode="scaleToFill"
          @click="clearAmountClick"
        />
      </view>

      <view class="amount-select-box">
        <view
          v-for="item in amountArr"
          :key="item.id"
          class="item"
          :class="{ active: amount === item.num }"
          @click="handleClick(item)"
        >
          {{ item.id === 3 ? item.text : item.num }}
        </view>
      </view>
      <view class="amount-rate-container">
        参考年化利率
        <text class="amount-rate-text">7.2%</text>
        起,1000元1天仅需0.3元
      </view>
    </view>

    <view class="amount-info-container">
      <view class="amount-info-plan">
        <view class="amount-info-plan-title">借款期限</view>
        <view class="amount-info-plan-amount">
          <view class="amount-info-plan-title">可选3,6,12,36期</view>
        </view>
      </view>
      <view class="amount-info-plan">
        <view class="amount-info-plan-title">还款规则</view>
        <view class="amount-info-plan-amount">
          <view class="amount-info-plan-title">随借随还</view>
          <view class="amount-info-tips">按日计息,提前还0手续费</view>
        </view>
      </view>
    </view>

    <view class="amount-info-input-container">
      <view class="amount-info-input-item">
        <view class="code-input-btn"> 手机号 </view>
        <input
          class="phone-input"
          type="number"
          placeholder="请输入手机号"
          v-model="phone"
          @blur="phoneInputBlur"
          maxlength="11"
        />
      </view>

      <!-- 人机验证组件 -->
      <aliyun-captcha
        v-show="shouldShowCaptcha"
        @success="onCaptchaSuccess"
        @fail="onCaptchaFail"
        @error="onCaptchaError"
      >
        <view class="amount-info-btn"> 立即申请 </view>
      </aliyun-captcha>

      <!-- 普通按钮 -->
      <view v-show="!shouldShowCaptcha" class="amount-info-btn" @click="phoneBtnClick"> 立即申请 </view>

      <view class="amount-tip"> 最终借款额度，借款利率以实际审批为准 </view>
    </view>
    <img
      class="declare-img"
      src="https://cdn.oss-unos.hmctec.cn/common/path/1d4faa0f8fc343ec92a909fc954facca.png"
      alt=""
    />
    <view class="declare-container">
      <view>郑重声明</view>
      <view>
        本平台只提供贷款咨询和推荐服务，
        放款由银行或金融机构进行，所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全
      </view>
      <view>请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。证您的资金安全</view>
    </view>

  </view>
</template>

<script>
import { reportUV, getDcProductLink, saveAptitude } from '@/apis/common'
import { behaviorVerify } from '@/apis/user'
import { encryptByDES } from '@/utils/encrypt'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import AliyunCaptcha from '@/components/captcha/aliyun-captcha.vue'

export default {
  name: 'v151',
  components: {
    AliyunCaptcha
  },

  computed: {
    shouldShowCaptcha() {
      return this.$u.test.mobile(this.phone);
    }
  },

  data() {
    return {
      amount: 50000,
      periodList: [36, 24, 12, 6],
      period: 36,
      monthlyPay: '',
      phone: '',
      selectedNum: '',
      captchaVerifyParam: null,
      amountArr: [
        { id: 1, num: 50000 },
        { id: 2, num: 100000 },
        { id: 3, num: 200000, text: '全部借出' }
      ]
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  mounted() {
    this.selectedNum = 50000
    if (this.channelId) {
      // 上报UV
      reportUV({ channelId: this.channelId })
    }
    this.computedMonthPay()
    uni.setNavigationBarTitle({
      title: '星惠花'
    })
  },
  methods: {
    handleClick(item) {
      this.selectedNum = item.num
      this.amount = item.num
    },
    amountInputBlur() {
      if (isNaN(this.amount)) {
        this.amount = 200000
        return
      }

      if (this.amount < 50000) {
        this.amount = 50000
        return
      }

      if (this.amount > 200000) {
        this.amount = 200000
        return
      }

      this.computedMonthPay()
    },

    clearAmountClick() {
      this.amount = 200000
      this.computedMonthPay()
    },

    periodClick(item) {
      this.period = item
      this.computedMonthPay()
    },

    computedMonthPay() {
      let price = Number(this.amount)
      let mLatte = (price * 12) / 100 / 12
      const month = this.period

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    phoneInputBlur() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },




    phoneBtnClick() {
      if (!this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
    },

    // 人机验证成功回调
    async onCaptchaSuccess(captchaVerifyParam) {
      this.captchaVerifyParam = captchaVerifyParam

      // 显示验证成功提示
      uni.showToast({
        title: '验证成功',
        icon: 'success',
        duration: 1500
      })

      // 先调用行为验证接口进行后端校验
      try {
        uni.showLoading({
          title: '验证中...',
          mask: true
        })

        const verifyRes = await behaviorVerify({
          phone: this.phone,
          template: 'v151',
          channelId: this.channelId,
          captchaVerifyParam
        })

        uni.hideLoading()

        if (verifyRes.code !== 200 || verifyRes.data !== true) {
          uni.showToast({
            title: '验证失败',
            icon: 'none'
          })
          this.resetCaptcha()
          return
        }

        // 验证通过后，直接进行登录
        this.login()
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        })
        this.resetCaptcha()
      }
    },

    // 人机验证失败回调
    onCaptchaFail(error) {
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 人机验证错误回调
    onCaptchaError(error) {
      uni.showToast({
        title: '验证组件加载失败',
        icon: 'none'
      })
      this.resetCaptcha()
    },

    // 重置验证状态
    resetCaptcha() {
      this.captchaVerifyParam = null;
    },

    async getDcProductLink(consumerId) {
      uni.showLoading({
        title: '加载中',
        mask: true
      })

      try {
        const res = await getDcProductLink({ consumerId })
        return res.data || ''
      } catch (error) {
        return ''
      } finally {
        uni.hideLoading()
      }
    },

    async login() {
      try {
        const params = {
          phone: this.phone,
          channelId: this.channelId,
          demandAmount: this.amount,
          deviceType: uni.getSystemInfoSync().platform,
          phoneBlack: getBlackPhone()
        }

        const res = await saveAptitude(params)

        if (res.code != 200) {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          })
          return
        }

        // 登录成功后，记录手机号到黑名单
        setBlackPhone(params.phone)
        this.$u.vuex('vuex_phone', params.phone)
        this.$u.vuex('vuex_consumerId', res.data)

        const urlParam = {
          consumerId: res.data,
          phone: this.phone,
          demandAmount: this.amount,
          channelId: this.channelId,
          monthIndex: this.periodList.indexOf(this.period)
        }

        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

        const link = await this.getDcProductLink(urlParam.consumerId)
        this.$u.vuex('vuex_overloanWebview.url', link)

        if (link) {
          uni.navigateTo({
            url: `/extreme/v151/overloanWebview/index?param=${urlParamString}`
          })
        } else {
          uni.navigateTo({
            url: `/extreme/v151/wechatOfficialAccount/index?param=${urlParamString}`
          })
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  // background: linear-gradient(180deg, #304ac8 43%, rgba(216, 216, 216, 0) 100%);
}

.tips-container {
  .tips-content {
    display: flex;
    align-items: center;
    gap: 10rpx;
    background: rgba(255, 255, 255, 0.19);
    padding: 18rpx 35rpx;

    .tips-img-container {
      width: 40rpx;
      height: 40rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .tips-img {
      width: 26rpx;
      height: 27rpx;
    }

    .tips-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 40rpx;
      margin: 0;
      padding: 0;
    }
  }
}

.amount-container {
  padding: 20px 30rpx 40rpx;
  margin: 30rpx 30rpx 20rpx;
  background: #fff;
  border-radius: 20rpx;

  .amount-title {
    font-size: 28rpx;
    color: #999999;
    line-height: 39rpx;
    text-align: center;
  }

  .amount-input-container {
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25rpx;
    padding-bottom: 20rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/1dd0599a66a34f979ef19389fa2244fc.png');
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: 25%;
    .amount-input-icon {
      color: #131734;
      font-weight: 700;
      font-size: 72rpx;
    }
    .amount-input {
      font-family: D-DIN, D-DIN;
      font-weight: 700;
      font-size: 108rpx;
      color: #131734;
      line-height: 137rpx;
      width: 320rpx;
    }

    .amount-input-img {
      width: 28rpx;
      height: 28rpx;
      flex-shrink: 0;
      transform: translateY(20rpx);
    }
  }

  .amount-rate-container {
    margin-top: 40rpx;

    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 36rpx;
    text-align: center;

    .amount-rate-text {
      color: #ff8827;
    }
  }
  .amount-select-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;
    padding: 0 60rpx;
    .item {
      border-radius: 36rpx 36rpx 36rpx 36rpx;
      border: 1rpx solid #999999;
      padding: 11rpx 24rpx;
      font-size: 32rpx;
      color: #999999;
      line-height: 40rpx;
    }
    .active {
      color: #ff8827;
      border: 1rpx solid #ff8827;
    }
  }
}

.amount-info-container {
  padding: 10rpx 30rpx 40rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 0 30rpx;

  .amount-info-title {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 41rpx;
  }

  .amount-info-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 34rpx;
    gap: 30rpx;

    .amount-info-list-item {
      width: 133rpx;
      height: 60rpx;
      background: #f7f7f7;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 1rpx solid #f7f7f7;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Arial, Arial;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      &.selected {
        background: #d6ddff;
        color: #304ac8;
        border-color: #304ac8;
      }
    }
  }

  .amount-info-plan {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;

    .amount-info-plan-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
    }

    .amount-info-plan-amount {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      text-align: right;
    }
  }

  .amount-info-tips {
    font-weight: 400;
    font-size: 20rpx;
    color: #999999;
    line-height: 35rpx;
  }
}

.amount-info-input-container {
  margin: 20rpx 30rpx 40rpx;
  padding: 45rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;

  .amount-info-input-item {
    padding: 28rpx 47rpx;
    background: #f6f6f6;
    border-radius: 723rpx 723rpx 723rpx 723rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15rpx;

    &.code-input-item {
      margin-bottom: 35rpx;
    }

    .phone-input,
    .code-input {
      font-weight: 400;
      font-size: 32rpx;
      line-height: 36rpx;
    }

    .code-input-btn {
      font-weight: 400;
      font-size: 36rpx;
      color: #333333;
      line-height: 36rpx;
    }
  }

  .amount-info-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ff8827;
    border-radius: 723rpx 723rpx 723rpx 723rpx;
    padding: 22rpx;
    font-weight: 400;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 58rpx;
    margin-top: 24rpx;
  }
  .amount-tip {
    text-align: center;
    font-size: 24rpx;
    color: #999999;
    line-height: 35rpx;
    font-style: normal;
    text-transform: none;
    margin-top: 20rpx;
  }
}

.declare-img {
  width: 100%;
}
.declare-container {
  padding: 40rpx 60rpx;
  font-weight: normal;
  font-size: 24rpx;
  color: #939393;
  line-height: 34rpx;
  text-align: center;
}

</style>
